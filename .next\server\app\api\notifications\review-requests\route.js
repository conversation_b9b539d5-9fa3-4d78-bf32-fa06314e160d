"use strict";(()=>{var e={};e.id=3193,e.ids=[3193],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},95439:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>E,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>f,serverHooks:()=>w,staticGenerationAsyncStorage:()=>N});var i={};t.r(i),t.d(i,{GET:()=>y,POST:()=>m});var o=t(49303),s=t(88716),a=t(60670),n=t(87070),d=t(75571),u=t(95306),c=t(3474),p=t(89585),l=t(54211);async function m(e){try{let e=await (0,d.getServerSession)(u.L);if(!e?.user?.id||"ADMIN"!==e.user.role)return n.NextResponse.json({error:"Admin access required"},{status:401});l.kg.info("Starting review request notifications for delivered orders");let r=new Date;r.setDate(r.getDate()-7);let t=await c._.order.findMany({where:{status:"DELIVERED",updatedAt:{gte:r}},include:{user:{include:{preferences:!0}},items:{include:{product:!0}}}}),i=0;for(let e of t)try{let r=e.user.preferences;if(!r?.reviewNotifications||await c._.notification.findFirst({where:{userId:e.userId,type:"REVIEW_REQUEST",data:{path:["orderId"],equals:e.id}}}))continue;let t=await c._.review.findMany({where:{userId:e.userId,productId:{in:e.items.map(e=>e.productId)}}});if(0===t.length){let r=e.items.map(e=>e.productId),t=e.items.map(e=>e.product.name);await p.kg.reviewRequest(e.userId,{orderId:e.id,orderNumber:e.orderNumber,productIds:r,productNames:t}),i++,l.kg.info("Review request notification sent",{orderId:e.id,orderNumber:e.orderNumber,userId:e.userId,productCount:e.items.length})}}catch(r){l.kg.error("Error processing order for review request",r,{orderId:e.id,orderNumber:e.orderNumber})}return l.kg.info("Review request notifications completed",{totalOrders:t.length,notificationsSent:i}),n.NextResponse.json({success:!0,message:"Review request notifications completed",stats:{totalOrders:t.length,notificationsSent:i}})}catch(e){return l.kg.error("Failed to send review request notifications",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){try{let e=await (0,d.getServerSession)(u.L);if(!e?.user?.id||"ADMIN"!==e.user.role)return n.NextResponse.json({error:"Admin access required"},{status:401});let r=new Date;r.setDate(r.getDate()-30);let[t,i,o]=await Promise.all([c._.order.count({where:{status:"DELIVERED",updatedAt:{gte:r}}}),c._.notification.count({where:{type:"REVIEW_REQUEST",createdAt:{gte:r}}}),c._.review.count({where:{createdAt:{gte:r}}})]),s=i>0?Math.round(o/i*100):0;return n.NextResponse.json({success:!0,stats:{deliveredOrders:t,reviewRequestsSent:i,reviewsSubmitted:o,conversionRate:`${s}%`,period:"Last 30 days"}})}catch(e){return l.kg.error("Failed to get review request statistics",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/notifications/review-requests/route",pathname:"/api/notifications/review-requests",filename:"route",bundlePath:"app/api/notifications/review-requests/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\review-requests\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:g,staticGenerationAsyncStorage:N,serverHooks:w}=f,E="/api/notifications/review-requests/route";function v(){return(0,a.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:N})}},89585:(e,r,t)=>{t.d(r,{$T:()=>n,aZ:()=>o,kg:()=>a,un:()=>s});var i=t(68602);let o={orderPlaced:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_PLACED,title:"Order Placed Successfully",message:`Your order #${r.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,amount:r.total,currency:r.currency,itemCount:r.itemCount},priority:i.RX.HIGH,sendEmail:!0}),orderConfirmed:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_CONFIRMED,title:"Order Confirmed",message:`Your order #${r.orderNumber} has been confirmed and is being prepared for shipment.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:i.RX.NORMAL,sendEmail:!0}),orderProcessing:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_PROCESSING,title:"Order Being Processed",message:`Your order #${r.orderNumber} is currently being processed. We'll notify you once it's shipped.`,data:{orderId:r.orderId,orderNumber:r.orderNumber},priority:i.RX.NORMAL,sendEmail:!0}),orderShipped:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_SHIPPED,title:"Order Shipped",message:`Great news! Your order #${r.orderNumber} has been shipped.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:i.RX.HIGH,sendEmail:!0}),orderDelivered:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_DELIVERED,title:"Order Delivered",message:`Your order #${r.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,deliveredAt:r.deliveredAt},priority:i.RX.HIGH,sendEmail:!0}),orderCancelled:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_CANCELLED,title:"Order Cancelled",message:`Your order #${r.orderNumber} has been cancelled.${r.reason?` Reason: ${r.reason}`:""}${r.refundAmount?` A refund of ${r.currency} ${r.refundAmount} will be processed within 3-5 business days.`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,reason:r.reason,refundAmount:r.refundAmount,currency:r.currency},priority:i.RX.HIGH,sendEmail:!0})},s={itemAdded:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.WISHLIST_ADDED,title:"Item Added to Wishlist",message:`${r.productName} has been added to your wishlist. We'll notify you of any price changes!`,data:{productId:r.productId,productName:r.productName,price:r.price,currency:r.currency},priority:i.RX.LOW,sendEmail:!1}),itemRemoved:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.WISHLIST_REMOVED,title:"Item Removed from Wishlist",message:`${r.productName} has been removed from your wishlist.`,data:{productId:r.productId,productName:r.productName},priority:i.RX.LOW,sendEmail:!1}),priceDropAlert:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.PRICE_DROP_ALERT,title:"Price Drop Alert!",message:`Great news! ${r.productName} is now ${r.discountPercentage}% off! Price dropped from ${r.currency} ${r.oldPrice} to ${r.currency} ${r.newPrice}.`,data:{productId:r.productId,productName:r.productName,oldPrice:r.oldPrice,newPrice:r.newPrice,currency:r.currency,discountPercentage:r.discountPercentage},priority:i.RX.HIGH,sendEmail:!0,expiresAt:new Date(Date.now()+6048e5)})},a={async reviewRequest(e,r){let t=r.productNames.join(", ");return await i.BF.createNotification({userId:e,type:i.k$.REVIEW_REQUEST,title:"How was your experience?",message:`We'd love to hear about your experience with ${t}. Your review helps other customers make informed decisions!`,data:{orderId:r.orderId,orderNumber:r.orderNumber,productIds:r.productIds,productNames:r.productNames},priority:i.RX.NORMAL,sendEmail:!0,expiresAt:new Date(Date.now()+2592e6)})},reviewSubmitted:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.REVIEW_SUBMITTED,title:"Review Submitted",message:`Thank you for your ${r.rating}-star review of ${r.productName}! Your feedback is valuable to us and other customers.`,data:{productId:r.productId,productName:r.productName,rating:r.rating},priority:i.RX.LOW,sendEmail:!1})},n={adminMessage:async(e,r)=>await i.BF.createNotification({userId:e,type:r.type||i.k$.ADMIN_MESSAGE,title:r.title,message:r.content,data:{sentByAdmin:!0,sendEmail:!1!==r.sendEmail,sendInApp:!1!==r.sendInApp},priority:r.priority||i.RX.NORMAL,sendEmail:!1!==r.sendEmail}),systemAlert:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.SYSTEM,title:r.title,message:r.message,data:{severity:r.severity},priority:"critical"===r.severity?i.RX.URGENT:"high"===r.severity?i.RX.HIGH:i.RX.NORMAL,sendEmail:"critical"===r.severity||"high"===r.severity}),maintenanceNotice:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.SYSTEM,title:"Scheduled Maintenance",message:`Our system will be under maintenance from ${r.startTime} to ${r.endTime}. ${r.description||"We apologize for any inconvenience."}`,data:{maintenanceStart:r.startTime,maintenanceEnd:r.endTime},priority:i.RX.HIGH,sendEmail:!0}),sendMessage:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ADMIN_MESSAGE,title:r.title,message:r.message,priority:r.priority||i.RX.NORMAL,sendEmail:r.sendEmail||!1}),sendBroadcast:async e=>await i.BF.sendBroadcast({type:i.k$.BROADCAST,title:e.title,message:e.message,priority:e.priority||i.RX.NORMAL,sendEmail:e.sendEmail||!1,userIds:e.userIds}),sendPromotion:async e=>await i.BF.sendBroadcast({type:i.k$.PROMOTIONAL,title:e.title,message:e.message,data:e.data,priority:i.RX.NORMAL,expiresAt:e.expiresAt,sendEmail:e.sendEmail||!1,userIds:e.userIds})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var o=t(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var n=o?Object.getOwnPropertyDescriptor(e,s):null;n&&(n.get||n.set)?Object.defineProperty(i,s,n):i[s]=e[s]}return i.default=e,t&&t.set(e,i),i}(t(45609));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[9276,5972,8691,6575,5245,2125],()=>t(95439));module.exports=i})();