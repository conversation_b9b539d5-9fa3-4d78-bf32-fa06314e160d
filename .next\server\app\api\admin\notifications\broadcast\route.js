"use strict";(()=>{var e={};e.id=3350,e.ids=[3350],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},68840:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>b,patchFetch:()=>j,requestAsyncStorage:()=>O,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>q});var n={};r.r(n),r.d(n,{POST:()=>y});var i=r(49303),o=r(88716),s=r(60670),a=r(87070),u=r(75571),p=r(95306),c=r(68602),d=r(54211),l=r(65630),f=r(29489);let x=l.Ry({title:l.Z_().min(1,"Title is required"),content:l.Z_().min(1,"Content is required"),type:l.Km(["BROADCAST","PROMOTIONAL","SYSTEM"]).default("BROADCAST"),priority:l.Km(["LOW","NORMAL","HIGH","URGENT"]).default("NORMAL"),sendEmail:l.O7().default(!0),sendInApp:l.O7().default(!0)});async function y(e){try{let t=await (0,u.getServerSession)(p.L);if(!t?.user?.id||"ADMIN"!==t.user.role)return a.NextResponse.json({error:"Admin access required"},{status:401});let r=await e.json(),n=x.parse(r);d.kg.info("Admin sending broadcast notification",{adminId:t.user.id,type:n.type,priority:n.priority,title:n.title});let i=await c.BF.sendBroadcast({type:n.type,title:n.title,message:n.content,priority:n.priority,sendEmail:n.sendEmail,data:{sentBy:t.user.id,sentAt:new Date().toISOString()}});return d.kg.info("Broadcast notification sent successfully",{adminId:t.user.id,userCount:i.length}),a.NextResponse.json({success:!0,message:`Broadcast notification sent to ${i.length} users`,userCount:i.length})}catch(e){if(e instanceof f.j)return a.NextResponse.json({error:"Validation error",details:e.issues},{status:400});return d.kg.error("Failed to send broadcast notification",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/notifications/broadcast/route",pathname:"/api/admin/notifications/broadcast",filename:"route",bundlePath:"app/api/admin/notifications/broadcast/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\broadcast\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:O,staticGenerationAsyncStorage:q,serverHooks:g}=m,b="/api/admin/notifications/broadcast/route";function j(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:q})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var i=r(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[9276,5972,8691,6575,9489,5245,5630,2125],()=>r(68840));module.exports=n})();