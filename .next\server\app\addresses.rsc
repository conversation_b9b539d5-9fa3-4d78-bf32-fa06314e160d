2:I[9107,[],"ClientPageRoot"]
3:I[4675,["1899","static/chunks/app/addresses/page-bde79f7ba13bd8a8.js"],"default",1]
4:I[4707,[],""]
5:I[6423,[],""]
6:I[578,["605","static/chunks/605-8908a61bc9b3ae18.js","3185","static/chunks/app/layout-033d6fce5bb17374.js"],"default"]
7:I[9124,["605","static/chunks/605-8908a61bc9b3ae18.js","3185","static/chunks/app/layout-033d6fce5bb17374.js"],"NotificationProvider"]
8:I[3827,["605","static/chunks/605-8908a61bc9b3ae18.js","3185","static/chunks/app/layout-033d6fce5bb17374.js"],"CartProvider"]
0:["DDn4ytjoX8dEY4um44fxO",[[["",{"children":["addresses",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",{"children":["addresses",{"children":["__PAGE__",{},[["$L1",["$","$L2",null,{"props":{"params":{},"searchParams":{}},"Component":"$3"}],null],null],null]},[null,["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children","addresses","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}]],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/085fec1fc60afdd5.css","precedence":"next","crossOrigin":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L6",null,{"children":["$","$L7",null,{"children":["$","$L8",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[]}]}]}]}]}]}]],null],null],["$L9",null]]]]
9:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"name":"theme-color","content":"#16a34a"}],["$","meta","2",{"charSet":"utf-8"}],["$","title","3",{"children":"Herbalicious - Natural Skincare"}],["$","meta","4",{"name":"description","content":"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients."}]]
1:null
