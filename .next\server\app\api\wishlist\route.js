"use strict";(()=>{var e={};e.id=9100,e.ids=[9100],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},81612:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>I,patchFetch:()=>R,requestAsyncStorage:()=>w,routeModule:()=>f,serverHooks:()=>h,staticGenerationAsyncStorage:()=>N});var i={};t.r(i),t.d(i,{DELETE:()=>y,GET:()=>l,POST:()=>m});var s=t(49303),o=t(88716),a=t(60670),n=t(87070),d=t(75571),u=t(95306),c=t(3474),p=t(89585);async function l(e){try{let e=await (0,d.getServerSession)(u.L);if(!e?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let r=(await c._.wishlistItem.findMany({where:{userId:e.user.id},include:{product:{include:{images:{orderBy:{id:"asc"},take:1},productCategories:{include:{category:!0}}}}},orderBy:{createdAt:"desc"}})).map(e=>({id:e.product.id,name:e.product.name,slug:e.product.slug,price:e.product.price,shortDescription:e.product.shortDescription,image:e.product.images[0]?.url||"/placeholder-product.jpg",rating:4.5,reviews:0,wishlistItemId:e.id}));return n.NextResponse.json({items:r})}catch(e){return console.error("Error fetching wishlist:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e){try{let r=await (0,d.getServerSession)(u.L);if(!r?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{productId:t}=await e.json();if(!t)return n.NextResponse.json({error:"Product ID is required"},{status:400});let i=await c._.product.findUnique({where:{id:t}});if(!i)return n.NextResponse.json({error:"Product not found"},{status:404});if(await c._.wishlistItem.findUnique({where:{userId_productId:{userId:r.user.id,productId:t}}}))return n.NextResponse.json({error:"Item already in wishlist"},{status:400});let s=await c._.wishlistItem.create({data:{userId:r.user.id,productId:t}});try{await p.un.itemAdded(r.user.id,{productId:i.id,productName:i.name,price:i.price||void 0,currency:"INR"})}catch(e){console.error("Failed to send wishlist added notification:",e)}return n.NextResponse.json({success:!0,item:s})}catch(e){return console.error("Error adding to wishlist:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){try{let r=await (0,d.getServerSession)(u.L);if(!r?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),i=t.get("productId");if(!i)return n.NextResponse.json({error:"Product ID is required"},{status:400});let s=await c._.product.findUnique({where:{id:i}}),o=await c._.wishlistItem.deleteMany({where:{userId:r.user.id,productId:i}});if(0===o.count)return n.NextResponse.json({error:"Item not found in wishlist"},{status:404});try{s&&await p.un.itemRemoved(r.user.id,{productId:s.id,productName:s.name})}catch(e){console.error("Failed to send wishlist removed notification:",e)}return n.NextResponse.json({success:!0})}catch(e){return console.error("Error removing from wishlist:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/wishlist/route",pathname:"/api/wishlist",filename:"route",bundlePath:"app/api/wishlist/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\wishlist\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:w,staticGenerationAsyncStorage:N,serverHooks:h}=f,I="/api/wishlist/route";function R(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:N})}},89585:(e,r,t)=>{t.d(r,{$T:()=>n,aZ:()=>s,kg:()=>a,un:()=>o});var i=t(68602);let s={orderPlaced:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_PLACED,title:"Order Placed Successfully",message:`Your order #${r.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,amount:r.total,currency:r.currency,itemCount:r.itemCount},priority:i.RX.HIGH,sendEmail:!0}),orderConfirmed:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_CONFIRMED,title:"Order Confirmed",message:`Your order #${r.orderNumber} has been confirmed and is being prepared for shipment.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:i.RX.NORMAL,sendEmail:!0}),orderProcessing:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_PROCESSING,title:"Order Being Processed",message:`Your order #${r.orderNumber} is currently being processed. We'll notify you once it's shipped.`,data:{orderId:r.orderId,orderNumber:r.orderNumber},priority:i.RX.NORMAL,sendEmail:!0}),orderShipped:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_SHIPPED,title:"Order Shipped",message:`Great news! Your order #${r.orderNumber} has been shipped.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:i.RX.HIGH,sendEmail:!0}),orderDelivered:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_DELIVERED,title:"Order Delivered",message:`Your order #${r.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,deliveredAt:r.deliveredAt},priority:i.RX.HIGH,sendEmail:!0}),orderCancelled:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ORDER_CANCELLED,title:"Order Cancelled",message:`Your order #${r.orderNumber} has been cancelled.${r.reason?` Reason: ${r.reason}`:""}${r.refundAmount?` A refund of ${r.currency} ${r.refundAmount} will be processed within 3-5 business days.`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,reason:r.reason,refundAmount:r.refundAmount,currency:r.currency},priority:i.RX.HIGH,sendEmail:!0})},o={itemAdded:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.WISHLIST_ADDED,title:"Item Added to Wishlist",message:`${r.productName} has been added to your wishlist. We'll notify you of any price changes!`,data:{productId:r.productId,productName:r.productName,price:r.price,currency:r.currency},priority:i.RX.LOW,sendEmail:!1}),itemRemoved:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.WISHLIST_REMOVED,title:"Item Removed from Wishlist",message:`${r.productName} has been removed from your wishlist.`,data:{productId:r.productId,productName:r.productName},priority:i.RX.LOW,sendEmail:!1}),priceDropAlert:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.PRICE_DROP_ALERT,title:"Price Drop Alert!",message:`Great news! ${r.productName} is now ${r.discountPercentage}% off! Price dropped from ${r.currency} ${r.oldPrice} to ${r.currency} ${r.newPrice}.`,data:{productId:r.productId,productName:r.productName,oldPrice:r.oldPrice,newPrice:r.newPrice,currency:r.currency,discountPercentage:r.discountPercentage},priority:i.RX.HIGH,sendEmail:!0,expiresAt:new Date(Date.now()+6048e5)})},a={async reviewRequest(e,r){let t=r.productNames.join(", ");return await i.BF.createNotification({userId:e,type:i.k$.REVIEW_REQUEST,title:"How was your experience?",message:`We'd love to hear about your experience with ${t}. Your review helps other customers make informed decisions!`,data:{orderId:r.orderId,orderNumber:r.orderNumber,productIds:r.productIds,productNames:r.productNames},priority:i.RX.NORMAL,sendEmail:!0,expiresAt:new Date(Date.now()+2592e6)})},reviewSubmitted:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.REVIEW_SUBMITTED,title:"Review Submitted",message:`Thank you for your ${r.rating}-star review of ${r.productName}! Your feedback is valuable to us and other customers.`,data:{productId:r.productId,productName:r.productName,rating:r.rating},priority:i.RX.LOW,sendEmail:!1})},n={adminMessage:async(e,r)=>await i.BF.createNotification({userId:e,type:r.type||i.k$.ADMIN_MESSAGE,title:r.title,message:r.content,data:{sentByAdmin:!0,sendEmail:!1!==r.sendEmail,sendInApp:!1!==r.sendInApp},priority:r.priority||i.RX.NORMAL,sendEmail:!1!==r.sendEmail}),systemAlert:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.SYSTEM,title:r.title,message:r.message,data:{severity:r.severity},priority:"critical"===r.severity?i.RX.URGENT:"high"===r.severity?i.RX.HIGH:i.RX.NORMAL,sendEmail:"critical"===r.severity||"high"===r.severity}),maintenanceNotice:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.SYSTEM,title:"Scheduled Maintenance",message:`Our system will be under maintenance from ${r.startTime} to ${r.endTime}. ${r.description||"We apologize for any inconvenience."}`,data:{maintenanceStart:r.startTime,maintenanceEnd:r.endTime},priority:i.RX.HIGH,sendEmail:!0}),sendMessage:async(e,r)=>await i.BF.createNotification({userId:e,type:i.k$.ADMIN_MESSAGE,title:r.title,message:r.message,priority:r.priority||i.RX.NORMAL,sendEmail:r.sendEmail||!1}),sendBroadcast:async e=>await i.BF.sendBroadcast({type:i.k$.BROADCAST,title:e.title,message:e.message,priority:e.priority||i.RX.NORMAL,sendEmail:e.sendEmail||!1,userIds:e.userIds}),sendPromotion:async e=>await i.BF.sendBroadcast({type:i.k$.PROMOTIONAL,title:e.title,message:e.message,data:e.data,priority:i.RX.NORMAL,expiresAt:e.expiresAt,sendEmail:e.sendEmail||!1,userIds:e.userIds})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var s=t(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var n=s?Object.getOwnPropertyDescriptor(e,o):null;n&&(n.get||n.set)?Object.defineProperty(i,o,n):i[o]=e[o]}return i.default=e,t&&t.set(e,i),i}(t(45609));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[9276,5972,8691,6575,5245,2125],()=>t(81612));module.exports=i})();