"use strict";exports.id=2125,exports.ids=[2125],exports.modules={95306:(e,t,r)=>{r.d(t,{L:()=>l});var i=r(13539),o=r(77234),a=r(53797),s=r(98691),n=r(3474);let l={adapter:(0,i.N)(n._),providers:[(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await n._.user.findUnique({where:{email:e.email}});if(!t||!t.password||!await s.qu(e.password,t.password))throw Error("Invalid credentials");return{id:t.id,email:t.email,name:t.name,role:t.role}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.sub=t.id,e.role=t.role),r&&e.email)try{let t=await n._.user.findUnique({where:{email:e.email},select:{id:!0,role:!0}});t&&(e.sub=t.id,e.role=t.role)}catch(e){}return e},async session({session:e,token:t}){if(t.email)try{let r=await n._.user.findUnique({where:{email:t.email},select:{id:!0,role:!0,email:!0,name:!0}});if(r)return{...e,user:{...e.user,id:r.id,role:r.role,email:r.email,name:r.name}}}catch(e){}return e.user&&t.sub?{...e,user:{...e.user,id:t.sub,role:t.role}}:e},redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t},events:{async signIn({user:e,account:t,profile:r,isNewUser:i}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},3474:(e,t,r)=>{r.d(t,{_:()=>o});var i=r(53524);let o=globalThis.prisma??new i.PrismaClient({log:["error"]})},95921:(e,t,r)=>{r.d(t,{Cz:()=>l,LS:()=>d,Pi:()=>c,bn:()=>p,jC:()=>u});var i=r(55245),o=r(54211);async function a(e){let t=process.env.BREVO_API_KEY;if(!t)throw Error("BREVO_API_KEY is not configured");let r={sender:{name:process.env.FROM_NAME||"Herbalicious",email:process.env.FROM_EMAIL||"<EMAIL>"},to:[{email:e.to}],subject:e.subject,htmlContent:e.html},i=await fetch("https://api.brevo.com/v3/smtp/email",{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json","api-key":t},body:JSON.stringify(r)});if(!i.ok){let t=await i.text();throw o.kg.emailError(e.to,e.subject,Error(`Brevo API error: ${i.status} - ${t}`)),Error(`Failed to send email via Brevo API: ${i.status}`)}o.kg.emailSent(e.to,e.subject,"brevo-api")}let s=()=>{if(process.env.SMTP_HOST)return i.createTransport({host:process.env.SMTP_HOST,port:parseInt(process.env.SMTP_PORT||"587"),secure:!1,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASS}});throw Error("No email configuration found")};async function n(e){let t=s(),r={from:e.from||process.env.FROM_EMAIL||"<EMAIL>",to:e.to,subject:e.subject,html:e.html};await t.sendMail(r),o.kg.emailSent(e.to,e.subject,"smtp")}async function l(e){try{if(process.env.BREVO_API_KEY){await a(e);return}await n(e)}catch(t){throw o.kg.emailError(e.to,e.subject,t),t}}async function d(e,t){let r=`${process.env.NEXTAUTH_URL}/reset-password?token=${t}`,i=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Password Reset Request</h2>
      <p>You have requested to reset your password for your Herbalicious account.</p>
      <p>Click the button below to reset your password:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${r}" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Reset Password
        </a>
      </div>
      <p>If the button doesn't work, copy and paste this link into your browser:</p>
      <p style="word-break: break-all; color: #666;">${r}</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        This link will expire in 1 hour. If you didn't request this password reset, please ignore this email.
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await l({to:e,subject:"Password Reset Request - Herbalicious",html:i})}async function c(e,t){let r=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Welcome to Herbalicious, ${t}!</h2>
      <p>Thank you for joining our community of natural health enthusiasts.</p>
      <p>We're excited to have you on board and look forward to helping you discover the best herbal products for your wellness journey.</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${process.env.NEXTAUTH_URL}/shop" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Start Shopping
        </a>
      </div>
      <p>If you have any questions, feel free to contact our support team.</p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await l({to:e,subject:"Welcome to Herbalicious!",html:r})}async function p(e,t){let r=t.items.map(e=>`
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${e.name}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${e.quantity}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">₹${e.price.toFixed(2)}</td>
    </tr>
  `).join(""),i=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Order Confirmation</h2>
      <p>Thank you for your order! Here are the details:</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin: 0 0 10px 0;">Order #${t.orderId}</h3>
      </div>
      
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Item</th>
            <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Qty</th>
            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Price</th>
          </tr>
        </thead>
        <tbody>
          ${r}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="2" style="padding: 12px; font-weight: bold; border-top: 2px solid #ddd;">Total:</td>
            <td style="padding: 12px; font-weight: bold; text-align: right; border-top: 2px solid #ddd;">₹${t.total.toFixed(2)}</td>
          </tr>
        </tfoot>
      </table>
      
      <div style="margin: 20px 0;">
        <h4>Shipping Address:</h4>
        <p style="background-color: #f9f9f9; padding: 10px; border-radius: 5px;">${t.shippingAddress}</p>
      </div>
      
      <p>We'll send you another email when your order ships.</p>
      
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await l({to:e,subject:`Order Confirmation - ${t.orderId}`,html:i})}async function u(e){let t=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Email Configuration Test</h2>
      <p>This is a test email to verify that your email configuration is working correctly.</p>
      <p>If you received this email, your Brevo integration is working properly!</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        Sent at: ${new Date().toISOString()}
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious for testing purposes.
      </p>
    </div>
  `;await l({to:e,subject:"Email Configuration Test - Herbalicious",html:t})}},54211:(e,t,r)=>{var i;r.d(t,{kg:()=>a}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:o,context:a,error:s,userId:n,requestId:l}=e,d=i[r],c=`[${t}] ${d}: ${o}`;return n&&(c+=` | User: ${n}`),l&&(c+=` | Request: ${l}`),a&&Object.keys(a).length>0&&(c+=` | Context: ${JSON.stringify(a)}`),s&&(c+=` | Error: ${s.message}`,this.isDevelopment&&s.stack&&(c+=`
Stack: ${s.stack}`)),c}log(e,t,r,i){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:i},a=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(a);break;case 1:console.warn(a);break;case 2:console.info(a);break;case 3:console.debug(a)}else console.log(JSON.stringify(o))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,i){this.info(`API ${e} ${t}`,{...i,userId:r,type:"api_request"})}apiResponse(e,t,r,i,o){this.info(`API ${e} ${t} - ${r}`,{...o,statusCode:r,duration:i,type:"api_response"})}apiError(e,t,r,i,o){this.error(`API ${e} ${t} failed`,r,{...o,userId:i,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,i){this.warn("Authentication failed",{...i,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,i){this.debug(`DB ${e} on ${t}`,{...i,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,i){this.error(`DB ${e} on ${t} failed`,r,{...i,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,i){this.info("Email sent",{...i,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,i){this.error("Email failed to send",r,{...i,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let a=new o},68602:(e,t,r)=>{r.d(t,{BF:()=>d,RX:()=>o,k$:()=>i});var i,o,a=r(3474),s=r(95921),n=r(54211);(function(e){e.ORDER_PLACED="ORDER_PLACED",e.ORDER_CONFIRMED="ORDER_CONFIRMED",e.ORDER_PROCESSING="ORDER_PROCESSING",e.ORDER_SHIPPED="ORDER_SHIPPED",e.ORDER_DELIVERED="ORDER_DELIVERED",e.ORDER_CANCELLED="ORDER_CANCELLED",e.WISHLIST_ADDED="WISHLIST_ADDED",e.WISHLIST_REMOVED="WISHLIST_REMOVED",e.PRICE_DROP_ALERT="PRICE_DROP_ALERT",e.REVIEW_REQUEST="REVIEW_REQUEST",e.REVIEW_SUBMITTED="REVIEW_SUBMITTED",e.ADMIN_MESSAGE="ADMIN_MESSAGE",e.BROADCAST="BROADCAST",e.PROMOTIONAL="PROMOTIONAL",e.SYSTEM="SYSTEM"})(i||(i={})),function(e){e.LOW="LOW",e.NORMAL="NORMAL",e.HIGH="HIGH",e.URGENT="URGENT"}(o||(o={}));class l{async createNotification(e){try{let t=await a._.userPreference.findUnique({where:{userId:e.userId}});if(!this.shouldSendNotification(e.type,t))return n.kg.info(`Notification skipped for user ${e.userId} due to preferences`),null;let r=await a._.notification.create({data:{userId:e.userId,type:e.type,title:e.title,message:e.message,data:e.data||{},priority:e.priority||"NORMAL",expiresAt:e.expiresAt,templateId:e.templateId},include:{user:!0,template:!0}});return e.sendEmail&&t?.emailNotifications!==!1&&await this.sendEmailNotification(r),n.kg.info(`Notification created: ${r.id} for user ${e.userId}`),r}catch(e){throw n.kg.error("Error creating notification:",e),e}}async sendBroadcast(e){try{let t;t=e.userIds&&e.userIds.length>0?await a._.user.findMany({where:{id:{in:e.userIds}},include:{preferences:!0}}):await a._.user.findMany({include:{preferences:!0},where:{OR:[{preferences:{broadcastMessages:!0}},{preferences:null}]}});let r=[];for(let i of t)if(this.shouldSendNotification(e.type,i.preferences)){let t=await a._.notification.create({data:{userId:i.id,type:e.type,title:e.title,message:e.message,data:e.data||{},priority:e.priority||"NORMAL",expiresAt:e.expiresAt,templateId:e.templateId}});r.push(t),e.sendEmail&&i.preferences?.emailNotifications!==!1&&await this.sendEmailNotification({...t,user:i,template:null})}return n.kg.info(`Broadcast sent to ${r.length} users`),r}catch(e){throw n.kg.error("Error sending broadcast:",e),e}}async markAsRead(e,t){try{return await a._.notification.update({where:{id:e,userId:t},data:{isRead:!0}})}catch(e){throw n.kg.error("Error marking notification as read:",e),e}}async markAllAsRead(e){try{return await a._.notification.updateMany({where:{userId:e,isRead:!1},data:{isRead:!0}})}catch(e){throw n.kg.error("Error marking all notifications as read:",e),e}}async getUserNotifications(e,t={}){try{let{page:r=1,limit:i=20,unreadOnly:o=!1,type:s}=t,n={userId:e,OR:[{expiresAt:null},{expiresAt:{gt:new Date}}]};o&&(n.isRead=!1),s&&(n.type=s);let[l,d]=await Promise.all([a._.notification.findMany({where:n,orderBy:{createdAt:"desc"},skip:(r-1)*i,take:i,include:{template:!0}}),a._.notification.count({where:n})]);return{notifications:l,total:d,page:r,limit:i,totalPages:Math.ceil(d/i)}}catch(e){throw n.kg.error("Error getting user notifications:",e),e}}async getUnreadCount(e){try{return await a._.notification.count({where:{userId:e,isRead:!1,OR:[{expiresAt:null},{expiresAt:{gt:new Date}}]}})}catch(e){throw n.kg.error("Error getting unread count:",e),e}}async cleanupOldNotifications(e=30){try{let t=new Date;t.setDate(t.getDate()-e);let r=await a._.notification.deleteMany({where:{createdAt:{lt:t},isRead:!0}});return n.kg.info(`Cleaned up ${r.count} old notifications`),r}catch(e){throw n.kg.error("Error cleaning up notifications:",e),e}}async sendEmailNotification(e){try{let t=e.user;if(!t?.email)throw Error("User email not found");let r=e.template?.emailSubject||e.title,i=this.generateEmailContent(e);await (0,s.Cz)({to:t.email,subject:r,html:i}),await a._.notification.update({where:{id:e.id},data:{emailSent:!0,emailSentAt:new Date}}),n.kg.info(`Email notification sent to ${t.email}`)}catch(t){await a._.notification.update({where:{id:e.id},data:{emailError:t instanceof Error?t.message:"Unknown error"}}),n.kg.error("Error sending email notification:",t)}}shouldSendNotification(e,t){if(!t)return!0;if(!t.inAppNotifications)return!1;switch(e){case"ORDER_PLACED":case"ORDER_CONFIRMED":case"ORDER_PROCESSING":case"ORDER_SHIPPED":case"ORDER_DELIVERED":case"ORDER_CANCELLED":return t.orderNotifications;case"WISHLIST_ADDED":case"WISHLIST_REMOVED":return t.wishlistNotifications;case"PRICE_DROP_ALERT":return t.priceDropAlerts;case"REVIEW_REQUEST":case"REVIEW_SUBMITTED":return t.reviewNotifications;case"ADMIN_MESSAGE":return t.adminMessages;case"BROADCAST":case"PROMOTIONAL":return t.broadcastMessages;default:return!0}}generateEmailContent(e){let t=process.env.NEXTAUTH_URL||"http://localhost:3000";return`
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #333; margin: 0 0 15px 0;">${e.title}</h2>
          <p style="color: #666; line-height: 1.6; margin: 0;">${e.message}</p>
        </div>
        
        ${e.data&&Object.keys(e.data).length>0?`
          <div style="margin-bottom: 20px;">
            <h3 style="color: #333; margin: 0 0 10px 0;">Details:</h3>
            ${this.formatNotificationData(e.data)}
          </div>
        `:""}
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${t}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Visit Herbalicious
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>You received this notification because you have an account with Herbalicious.</p>
          <p>You can manage your notification preferences in your account settings.</p>
        </div>
      </div>
    `}formatNotificationData(e){let t='<ul style="color: #666; line-height: 1.6;">';return e.orderNumber&&(t+=`<li><strong>Order Number:</strong> ${e.orderNumber}</li>`),e.productName&&(t+=`<li><strong>Product:</strong> ${e.productName}</li>`),e.amount&&e.currency&&(t+=`<li><strong>Amount:</strong> ${e.currency} ${e.amount}</li>`),t+="</ul>"}}let d=new l}};