"use strict";(()=>{var e={};e.id=3381,e.ids=[3381],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},37117:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>A,patchFetch:()=>v,requestAsyncStorage:()=>h,routeModule:()=>N,serverHooks:()=>w,staticGenerationAsyncStorage:()=>E});var a={};t.r(a),t.d(a,{POST:()=>g});var i=t(49303),s=t(88716),o=t(60670),n=t(87070),d=t(65630),c=t(75571),u=t(95306),p=t(3474),m=t(62822),l=t(84875),y=t(54211),R=t(8149),f=t(89585);let I=d.Ry({cartItems:d.IX(d.Ry({productId:d.Z_(),quantity:d.Rx().min(1),price:d.Rx().min(0)})),shippingAddress:d.Ry({firstName:d.Z_().min(1),lastName:d.Z_().min(1),address1:d.Z_().min(1),address2:d.Z_().optional(),city:d.Z_().min(1),state:d.Z_().min(1),postalCode:d.Z_().min(1),country:d.Z_().min(1),phone:d.Z_().min(1)}),totalAmount:d.Rx().min(1),appliedCoupons:d.IX(d.Ry({coupon:d.Ry({id:d.Z_(),code:d.Z_(),name:d.Z_()}),discountAmount:d.Rx().min(0)})).optional().default([])}),g=(0,l.lm)(async e=>{y.kg.apiRequest("POST","/api/payments/create-order"),await (0,R.er)(e,R.Xw,10);let r=await (0,c.getServerSession)(u.L);if(!r?.user?.id)throw new l._7("Authentication required");let t=await e.json(),{cartItems:a,shippingAddress:i,totalAmount:s,appliedCoupons:o}=I.parse(t);y.kg.info("Creating payment order",{userId:r.user.id,totalAmount:s,itemCount:a.length});let d=0,g=[];for(let e of a){let r=await p._.product.findUnique({where:{id:e.productId}});if(!r)throw new l.p8(`Product ${e.productId} not found`);let t=r.price||0;if(Math.abs(t-e.price)>.01)throw new l.p8(`Price mismatch for product ${e.productId}`);let a=t*e.quantity;d+=a,g.push({productId:e.productId,quantity:e.quantity,price:t,total:a})}let N=o.reduce((e,r)=>e+r.discountAmount,0),h=d,E=h-N;if(Math.abs(E-s)>.01)throw new l.p8(`Total amount mismatch. Expected: ${E}, Received: ${s}`);let w=(0,m.mT)(s);if(!(0,m.ml)(w))throw new l.p8("Invalid payment amount");let A=(0,m.My)("HERB"),v=`ORD-${Date.now()}-${Math.random().toString(36).substring(2,8).toUpperCase()}`;try{let e=await (0,m.iP)({amount:w,currency:"INR",receipt:A,notes:{userId:r.user.id,userEmail:r.user.email||"",orderNumber:v}}),t=await p._.order.create({data:{orderNumber:v,userId:r.user.id,status:"PENDING",paymentStatus:"PENDING",paymentId:e.id,subtotal:h,couponDiscount:N,total:s,currency:"INR",notes:`Razorpay Order ID: ${e.id}${o.length>0?` | Coupons: ${o.map(e=>e.coupon.code).join(", ")}`:""}`,address:{create:{firstName:i.firstName,lastName:i.lastName,address1:i.address1,address2:i.address2,city:i.city,state:i.state,postalCode:i.postalCode,country:i.country,phone:i.phone}},items:{create:g.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price,total:e.total}))},...o.length>0&&{couponUsages:{create:o.map(e=>({couponId:e.coupon.id,userId:r.user.id,usedAt:new Date}))}}},include:{items:{include:{product:!0}},address:!0}});y.kg.info("Payment order created successfully",{orderId:t.id,orderNumber:t.orderNumber,razorpayOrderId:e.id,amount:s,userId:r.user.id});try{await f.aZ.orderPlaced(r.user.id,{orderId:t.id,orderNumber:t.orderNumber,total:s,currency:"INR",itemCount:a.length}),y.kg.info("Order placed notification sent",{orderId:t.id,userId:r.user.id})}catch(e){y.kg.error("Failed to send order placed notification",e)}return n.NextResponse.json({success:!0,order:{id:t.id,orderNumber:t.orderNumber,razorpayOrderId:e.id,amount:w,currency:"INR",receipt:A},razorpayKeyId:"rzp_test_H8VYcEtWS9hwc8"})}catch(e){throw y.kg.error("Failed to create payment order",e),new l.gz("Failed to create payment order",500)}}),N=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/payments/create-order/route",pathname:"/api/payments/create-order",filename:"route",bundlePath:"app/api/payments/create-order/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\create-order\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:E,serverHooks:w}=N,A="/api/payments/create-order/route";function v(){return(0,o.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:E})}},84875:(e,r,t)=>{t.d(r,{AY:()=>u,M_:()=>d,_7:()=>n,dR:()=>c,gz:()=>s,lm:()=>m,p8:()=>o});var a=t(87070),i=t(29489);class s extends Error{constructor(e,r=500,t="INTERNAL_ERROR",a){super(e),this.statusCode=r,this.code=t,this.details=a,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,s)}}class o extends s{constructor(e,r){super(e,400,"VALIDATION_ERROR",r),this.name="ValidationError"}}class n extends s{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class d extends s{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class c extends s{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class u extends s{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class p extends s{constructor(e,r){super(e,500,"DATABASE_ERROR",r),this.name="DatabaseError"}}function m(e){return async(...r)=>{try{return await e(...r)}catch(e){return function(e){if(e instanceof s)return a.NextResponse.json({success:!1,error:{code:e.code,message:e.message,...e.details&&{details:e.details}}},{status:e.statusCode});if(e instanceof i.j){let r=new o("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))});return a.NextResponse.json({success:!1,error:{code:r.code,message:r.message,details:r.details}},{status:r.statusCode})}if(e&&"object"==typeof e&&"code"in e&&"string"==typeof e.code){let r=function(e){switch(e.code){case"P2002":let r=e.meta?.target?.[0]||"field";return new u(`${r} already exists`);case"P2003":let t=e.meta?.constraint;if(t?.includes("userId"))return new n("Invalid user session");return new o("Invalid reference to related record");case"P2025":case"P2001":return new c;case"P2014":return new o("Missing required relationship");case"P2000":return new o("Input value is too long");case"P2004":return new o("Data constraint violation");default:return new p("Database operation failed",{code:e.code,message:e.message})}}(e);return a.NextResponse.json({success:!1,error:{code:r.code,message:r.message,...r.details&&{details:r.details}}},{status:r.statusCode})}return e instanceof Error&&e.message,a.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Internal server error"}},{status:500})}(e)}}}},89585:(e,r,t)=>{t.d(r,{$T:()=>n,aZ:()=>i,kg:()=>o,un:()=>s});var a=t(68602);let i={orderPlaced:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.ORDER_PLACED,title:"Order Placed Successfully",message:`Your order #${r.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,amount:r.total,currency:r.currency,itemCount:r.itemCount},priority:a.RX.HIGH,sendEmail:!0}),orderConfirmed:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.ORDER_CONFIRMED,title:"Order Confirmed",message:`Your order #${r.orderNumber} has been confirmed and is being prepared for shipment.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:a.RX.NORMAL,sendEmail:!0}),orderProcessing:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.ORDER_PROCESSING,title:"Order Being Processed",message:`Your order #${r.orderNumber} is currently being processed. We'll notify you once it's shipped.`,data:{orderId:r.orderId,orderNumber:r.orderNumber},priority:a.RX.NORMAL,sendEmail:!0}),orderShipped:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.ORDER_SHIPPED,title:"Order Shipped",message:`Great news! Your order #${r.orderNumber} has been shipped.${r.estimatedDelivery?` Estimated delivery: ${r.estimatedDelivery}`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,estimatedDelivery:r.estimatedDelivery},priority:a.RX.HIGH,sendEmail:!0}),orderDelivered:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.ORDER_DELIVERED,title:"Order Delivered",message:`Your order #${r.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,data:{orderId:r.orderId,orderNumber:r.orderNumber,deliveredAt:r.deliveredAt},priority:a.RX.HIGH,sendEmail:!0}),orderCancelled:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.ORDER_CANCELLED,title:"Order Cancelled",message:`Your order #${r.orderNumber} has been cancelled.${r.reason?` Reason: ${r.reason}`:""}${r.refundAmount?` A refund of ${r.currency} ${r.refundAmount} will be processed within 3-5 business days.`:""}`,data:{orderId:r.orderId,orderNumber:r.orderNumber,reason:r.reason,refundAmount:r.refundAmount,currency:r.currency},priority:a.RX.HIGH,sendEmail:!0})},s={itemAdded:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.WISHLIST_ADDED,title:"Item Added to Wishlist",message:`${r.productName} has been added to your wishlist. We'll notify you of any price changes!`,data:{productId:r.productId,productName:r.productName,price:r.price,currency:r.currency},priority:a.RX.LOW,sendEmail:!1}),itemRemoved:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.WISHLIST_REMOVED,title:"Item Removed from Wishlist",message:`${r.productName} has been removed from your wishlist.`,data:{productId:r.productId,productName:r.productName},priority:a.RX.LOW,sendEmail:!1}),priceDropAlert:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.PRICE_DROP_ALERT,title:"Price Drop Alert!",message:`Great news! ${r.productName} is now ${r.discountPercentage}% off! Price dropped from ${r.currency} ${r.oldPrice} to ${r.currency} ${r.newPrice}.`,data:{productId:r.productId,productName:r.productName,oldPrice:r.oldPrice,newPrice:r.newPrice,currency:r.currency,discountPercentage:r.discountPercentage},priority:a.RX.HIGH,sendEmail:!0,expiresAt:new Date(Date.now()+6048e5)})},o={async reviewRequest(e,r){let t=r.productNames.join(", ");return await a.BF.createNotification({userId:e,type:a.k$.REVIEW_REQUEST,title:"How was your experience?",message:`We'd love to hear about your experience with ${t}. Your review helps other customers make informed decisions!`,data:{orderId:r.orderId,orderNumber:r.orderNumber,productIds:r.productIds,productNames:r.productNames},priority:a.RX.NORMAL,sendEmail:!0,expiresAt:new Date(Date.now()+2592e6)})},reviewSubmitted:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.REVIEW_SUBMITTED,title:"Review Submitted",message:`Thank you for your ${r.rating}-star review of ${r.productName}! Your feedback is valuable to us and other customers.`,data:{productId:r.productId,productName:r.productName,rating:r.rating},priority:a.RX.LOW,sendEmail:!1})},n={adminMessage:async(e,r)=>await a.BF.createNotification({userId:e,type:r.type||a.k$.ADMIN_MESSAGE,title:r.title,message:r.content,data:{sentByAdmin:!0,sendEmail:!1!==r.sendEmail,sendInApp:!1!==r.sendInApp},priority:r.priority||a.RX.NORMAL,sendEmail:!1!==r.sendEmail}),systemAlert:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.SYSTEM,title:r.title,message:r.message,data:{severity:r.severity},priority:"critical"===r.severity?a.RX.URGENT:"high"===r.severity?a.RX.HIGH:a.RX.NORMAL,sendEmail:"critical"===r.severity||"high"===r.severity}),maintenanceNotice:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.SYSTEM,title:"Scheduled Maintenance",message:`Our system will be under maintenance from ${r.startTime} to ${r.endTime}. ${r.description||"We apologize for any inconvenience."}`,data:{maintenanceStart:r.startTime,maintenanceEnd:r.endTime},priority:a.RX.HIGH,sendEmail:!0}),sendMessage:async(e,r)=>await a.BF.createNotification({userId:e,type:a.k$.ADMIN_MESSAGE,title:r.title,message:r.message,priority:r.priority||a.RX.NORMAL,sendEmail:r.sendEmail||!1}),sendBroadcast:async e=>await a.BF.sendBroadcast({type:a.k$.BROADCAST,title:e.title,message:e.message,priority:e.priority||a.RX.NORMAL,sendEmail:e.sendEmail||!1,userIds:e.userIds}),sendPromotion:async e=>await a.BF.sendBroadcast({type:a.k$.PROMOTIONAL,title:e.title,message:e.message,data:e.data,priority:a.RX.NORMAL,expiresAt:e.expiresAt,sendEmail:e.sendEmail||!1,userIds:e.userIds})}},62822:(e,r,t)=>{t.d(r,{FT:()=>c,My:()=>m,iP:()=>d,mT:()=>p,ml:()=>l,sS:()=>u});var a=t(41212),i=t.n(a),s=t(54211),o=t(84875);let n=new(i())({key_id:process.env.RAZORPAY_KEY_ID,key_secret:process.env.RAZORPAY_KEY_SECRET});async function d(e){try{s.kg.info("Creating Razorpay order",{amount:e.amount,currency:e.currency,receipt:e.receipt});let r=await n.orders.create({amount:e.amount,currency:e.currency,receipt:e.receipt,notes:e.notes||{}});return s.kg.info("Razorpay order created successfully",{orderId:r.id,amount:r.amount,receipt:r.receipt}),r}catch(e){throw s.kg.error("Failed to create Razorpay order",e),new o.gz("Failed to create payment order",500)}}function c(e){try{let r=t(84770).createHmac("sha256",process.env.RAZORPAY_KEY_SECRET).update(`${e.razorpay_order_id}|${e.razorpay_payment_id}`).digest("hex")===e.razorpay_signature;return s.kg.info("Payment signature verification",{orderId:e.razorpay_order_id,paymentId:e.razorpay_payment_id,isValid:r}),r}catch(e){return s.kg.error("Payment signature verification failed",e),!1}}async function u(e){try{s.kg.info("Fetching payment details",{paymentId:e});let r=await n.payments.fetch(e);return s.kg.info("Payment details fetched successfully",{paymentId:r.id,status:r.status,amount:r.amount}),r}catch(e){throw s.kg.error("Failed to fetch payment details",e),new o.gz("Failed to fetch payment details",500)}}function p(e){return Math.round(100*e)}function m(e="ORDER"){let r=Date.now(),t=Math.random().toString(36).substring(2,8).toUpperCase();return`${e}_${r}_${t}`}function l(e){return e>=100&&e<=15e8&&Number.isInteger(e)}},8149:(e,r,t)=>{t.d(r,{Ri:()=>s,Xw:()=>o,er:()=>d,jO:()=>n});var a=t(919);function i(e){let r=new a.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,t)=>new Promise((a,i)=>{let s=r.get(t)||[0];0===s[0]&&r.set(t,s),s[0]+=1,s[0]>=e?i(Error("Rate limit exceeded")):a()})}}let s=i({interval:9e5,uniqueTokenPerInterval:500}),o=i({interval:6e4,uniqueTokenPerInterval:500}),n=i({interval:36e5,uniqueTokenPerInterval:500});async function d(e,r,t){let a=function(e){let r=e.headers.get("x-forwarded-for"),t=e.headers.get("x-real-ip");return r?r.split(",")[0].trim():t||"unknown"}(e);try{await r.check(t,a)}catch(e){throw Error("Too many requests. Please try again later.")}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[9276,5972,8691,6575,9489,5245,5630,138,656,2125],()=>t(37117));module.exports=a})();