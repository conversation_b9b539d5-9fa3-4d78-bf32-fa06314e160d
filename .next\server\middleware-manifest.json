{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/profile(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/profile/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/edit-profile(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/edit-profile/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/order-history(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/order-history/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/addresses(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/addresses/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/wishlist(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/wishlist/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/cart(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/cart/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/login(.json)?[\\/#\\?]?$", "originalSource": "/login"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/signup(.json)?[\\/#\\?]?$", "originalSource": "/signup"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "d5cBx9tYcH2_AoGg5XGdN", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RQRpwzSEf2ZqvIqdMLASiiTJqWBwpS7WPI1o2EKsXuA=", "__NEXT_PREVIEW_MODE_ID": "f976b7033d022897da83ccdd938d57f7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c291802878b6f8dba6bd90229ac80dbc950ed2e36be8b17a3a32b30b3bbe71c0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "310927caad763efd5ae558077eb0226335231bb4981092f289d75045bcd3a014"}}}, "functions": {}, "sortedMiddleware": ["/"]}