{"/_not-found/page": "/_not-found", "/about/page": "/about", "/addresses/page": "/addresses", "/api/admin/notifications/broadcast/route": "/api/admin/notifications/broadcast", "/api/admin/notifications/stats/route": "/api/admin/notifications/stats", "/api/admin/notifications/history/route": "/api/admin/notifications/history", "/api/admin/notifications/send/route": "/api/admin/notifications/send", "/api/admin/notifications/templates/route": "/api/admin/notifications/templates", "/api/admin/users/route": "/api/admin/users", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/admin/reviews/route": "/api/admin/reviews", "/api/auth/reset-password/route": "/api/auth/reset-password", "/api/categories/route": "/api/categories", "/api/categories/[id]/route": "/api/categories/[id]", "/api/coupons/[id]/route": "/api/coupons/[id]", "/api/coupons/validate/route": "/api/coupons/validate", "/api/auth/session-debug/route": "/api/auth/session-debug", "/api/coupons/route": "/api/coupons", "/api/dashboard/stats/route": "/api/dashboard/stats", "/api/debug/r2-check/route": "/api/debug/r2-check", "/api/media/config/route": "/api/media/config", "/api/media/delete/route": "/api/media/delete", "/api/media/upload/route": "/api/media/upload", "/api/media/list/route": "/api/media/list", "/api/notifications/[id]/read/route": "/api/notifications/[id]/read", "/api/notifications/mark-all-read/route": "/api/notifications/mark-all-read", "/api/homepage-settings/route": "/api/homepage-settings", "/api/notifications/price-drop-check/route": "/api/notifications/price-drop-check", "/api/notifications/review-requests/route": "/api/notifications/review-requests", "/api/notifications/route": "/api/notifications", "/api/notifications/unread-count/route": "/api/notifications/unread-count", "/api/products/[id]/faqs/route": "/api/products/[id]/faqs", "/api/products/[id]/faqs/[faqId]/route": "/api/products/[id]/faqs/[faqId]", "/api/products/[id]/variations/[variationId]/route": "/api/products/[id]/variations/[variationId]", "/api/newsletter/route": "/api/newsletter", "/api/products/filters/route": "/api/products/filters", "/api/products/import/route": "/api/products/import", "/api/products/bulk/route": "/api/products/bulk", "/api/products/route": "/api/products", "/api/products/optimized/route": "/api/products/optimized", "/api/testimonials/[id]/route": "/api/testimonials/[id]", "/api/users/[id]/preferences/route": "/api/users/[id]/preferences", "/api/users/[id]/route": "/api/users/[id]", "/api/testimonials/route": "/api/testimonials", "/api/users/[id]/stats/route": "/api/users/[id]/stats", "/api/users/route": "/api/users", "/api/newsletter/export/route": "/api/newsletter/export", "/cart/page": "/cart", "/api/products/[id]/route": "/api/products/[id]", "/contact/page": "/contact", "/checkout/page": "/checkout", "/edit-profile/page": "/edit-profile", "/categories/page": "/categories", "/order-confirmation/page": "/order-confirmation", "/order-history/page": "/order-history", "/notifications/page": "/notifications", "/login/page": "/login", "/profile/page": "/profile", "/page": "/", "/product/[id]/page": "/product/[id]", "/signup/page": "/signup", "/shop/page": "/shop", "/sw.js/route": "/sw.js", "/wishlist/page": "/wishlist", "/api/wishlist/route": "/api/wishlist", "/api/auth/register/route": "/api/auth/register", "/api/auth/forgot-password/route": "/api/auth/forgot-password", "/api/orders/route": "/api/orders", "/api/payments/config/route": "/api/payments/config", "/api/payments/create-order/route": "/api/payments/create-order", "/api/payments/test/route": "/api/payments/test", "/api/orders/[id]/route": "/api/orders/[id]", "/api/payments/verify/route": "/api/payments/verify", "/api/products/[id]/reviews/route": "/api/products/[id]/reviews", "/api/products/[id]/variations/route": "/api/products/[id]/variations", "/api/test-email/route": "/api/test-email", "/admin/customers/page": "/admin/customers", "/admin/coupons/page": "/admin/coupons", "/admin/media/page": "/admin/media", "/admin/categories/page": "/admin/categories", "/admin/notifications/history/page": "/admin/notifications/history", "/admin/newsletter/page": "/admin/newsletter", "/admin/homepage/page": "/admin/homepage", "/admin/notifications/send/page": "/admin/notifications/send", "/admin/notifications/broadcast/page": "/admin/notifications/broadcast", "/admin/orders/page": "/admin/orders", "/admin/notifications/templates/page": "/admin/notifications/templates", "/admin/reviews/page": "/admin/reviews", "/admin/page": "/admin", "/admin/notifications/page": "/admin/notifications", "/admin/products/page": "/admin/products", "/admin/settings/page": "/admin/settings"}