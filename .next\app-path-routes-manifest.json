{"/_not-found/page": "/_not-found", "/about/page": "/about", "/addresses/page": "/addresses", "/api/admin/notifications/broadcast/route": "/api/admin/notifications/broadcast", "/api/admin/notifications/history/route": "/api/admin/notifications/history", "/api/admin/notifications/send/route": "/api/admin/notifications/send", "/api/admin/notifications/templates/route": "/api/admin/notifications/templates", "/api/admin/reviews/route": "/api/admin/reviews", "/api/admin/users/route": "/api/admin/users", "/api/admin/notifications/stats/route": "/api/admin/notifications/stats", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/auth/reset-password/route": "/api/auth/reset-password", "/api/auth/session-debug/route": "/api/auth/session-debug", "/api/categories/route": "/api/categories", "/api/categories/[id]/route": "/api/categories/[id]", "/api/coupons/route": "/api/coupons", "/api/coupons/[id]/route": "/api/coupons/[id]", "/api/coupons/validate/route": "/api/coupons/validate", "/api/dashboard/stats/route": "/api/dashboard/stats", "/api/homepage-settings/route": "/api/homepage-settings", "/api/debug/r2-check/route": "/api/debug/r2-check", "/api/media/config/route": "/api/media/config", "/api/media/delete/route": "/api/media/delete", "/api/media/list/route": "/api/media/list", "/api/media/upload/route": "/api/media/upload", "/api/newsletter/export/route": "/api/newsletter/export", "/api/newsletter/route": "/api/newsletter", "/api/notifications/mark-all-read/route": "/api/notifications/mark-all-read", "/api/notifications/[id]/read/route": "/api/notifications/[id]/read", "/api/notifications/review-requests/route": "/api/notifications/review-requests", "/api/notifications/price-drop-check/route": "/api/notifications/price-drop-check", "/api/notifications/route": "/api/notifications", "/api/notifications/unread-count/route": "/api/notifications/unread-count", "/api/products/[id]/faqs/[faqId]/route": "/api/products/[id]/faqs/[faqId]", "/api/products/[id]/faqs/route": "/api/products/[id]/faqs", "/api/products/[id]/route": "/api/products/[id]", "/api/products/[id]/variations/[variationId]/route": "/api/products/[id]/variations/[variationId]", "/api/products/bulk/route": "/api/products/bulk", "/api/products/filters/route": "/api/products/filters", "/api/products/import/route": "/api/products/import", "/api/products/optimized/route": "/api/products/optimized", "/api/products/route": "/api/products", "/api/testimonials/[id]/route": "/api/testimonials/[id]", "/api/testimonials/route": "/api/testimonials", "/api/users/[id]/preferences/route": "/api/users/[id]/preferences", "/api/users/[id]/route": "/api/users/[id]", "/api/users/[id]/stats/route": "/api/users/[id]/stats", "/api/wishlist/route": "/api/wishlist", "/api/users/route": "/api/users", "/cart/page": "/cart", "/categories/page": "/categories", "/checkout/page": "/checkout", "/login/page": "/login", "/edit-profile/page": "/edit-profile", "/contact/page": "/contact", "/notifications/page": "/notifications", "/order-history/page": "/order-history", "/order-confirmation/page": "/order-confirmation", "/page": "/", "/product/[id]/page": "/product/[id]", "/profile/page": "/profile", "/shop/page": "/shop", "/signup/page": "/signup", "/sw.js/route": "/sw.js", "/wishlist/page": "/wishlist", "/api/auth/forgot-password/route": "/api/auth/forgot-password", "/api/auth/register/route": "/api/auth/register", "/api/orders/route": "/api/orders", "/api/payments/config/route": "/api/payments/config", "/api/orders/[id]/route": "/api/orders/[id]", "/api/payments/test/route": "/api/payments/test", "/api/payments/verify/route": "/api/payments/verify", "/api/payments/create-order/route": "/api/payments/create-order", "/api/products/[id]/reviews/route": "/api/products/[id]/reviews", "/api/products/[id]/variations/route": "/api/products/[id]/variations", "/api/test-email/route": "/api/test-email", "/admin/categories/page": "/admin/categories", "/admin/coupons/page": "/admin/coupons", "/admin/customers/page": "/admin/customers", "/admin/media/page": "/admin/media", "/admin/homepage/page": "/admin/homepage", "/admin/notifications/broadcast/page": "/admin/notifications/broadcast", "/admin/newsletter/page": "/admin/newsletter", "/admin/notifications/history/page": "/admin/notifications/history", "/admin/notifications/page": "/admin/notifications", "/admin/notifications/send/page": "/admin/notifications/send", "/admin/notifications/templates/page": "/admin/notifications/templates", "/admin/orders/page": "/admin/orders", "/admin/page": "/admin", "/admin/settings/page": "/admin/settings", "/admin/reviews/page": "/admin/reviews", "/admin/products/page": "/admin/products"}